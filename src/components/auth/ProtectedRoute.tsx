import React from 'react';
import { Link, Navigate, useLocation } from 'react-router-dom';

import {
  ChromeSvg,
  EdgeSvg,
  ErrorSvg,
  FirefoxSvg,
  SafariSvg,
} from '@/assets/icons';
import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';

import { ROUTES } from '../../constants/routes';
import { useAuth } from '../../context/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const isDevEnvironment = () => {
  const url = typeof window !== 'undefined' ? window.location.href : '';
  return (
    url.includes('localhost') ||
    url.includes('***********') ||
    url.includes('192.168.') ||
    url.includes('10.0.')
  );
};

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = ROUTES.LOGIN,
}) => {
  const { isAuthenticated, isLoading, isError, isOnline, user } = useAuth();
  const location = useLocation();
  const isDev = isDevEnvironment();

  // Skip loading screen in development
  if (isLoading && !isDev) {
    return <MainLoaderSkeleton />;
  }

  // Show error UI only for user profile endpoint errors (not offline)
  if (isError && !isDev && isOnline) {
    return (
      <div className="z-100 fixed inset-0 mx-auto flex h-screen max-w-full items-center justify-center bg-white bg-cover">
        <div className="flex h-[400px] w-full items-center justify-center">
          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <ErrorSvg className="w-full max-w-[534px] max-xs:max-h-[150px]" />
            <div>
              <h4 className="mb-3 text-[16px] font-[600] text-primary max-sm:text-center sm:text-[24px]">
                Something went wrong
              </h4>
              <p className="mx-auto max-w-[525px] text-[12px] text-[#2F2F2F] max-sm:text-center sm:text-[14px]">
                Whoops, we've encountered an issue displaying the requested
                info. Please refresh or download the latest version of your
                browser.
              </p>
              <div className="mt-8 flex w-fit items-center gap-3 max-sm:mx-auto">
                <Link
                  target="_blank"
                  to="https://www.google.com/chrome/update/?_gl=1*6btuy2*_up*MQ..&gclid=EAIaIQobChMIx4jXqMXriAMVAYBQBh3T_BCHEAAYASAAEgLRLPD_BwE&gclsrc=aw.ds"
                >
                  <ChromeSvg />
                </Link>
                <Link
                  target="_blank"
                  to="https://www.mozilla.org/en-US/firefox/download/"
                >
                  <FirefoxSvg />
                </Link>
                <Link
                  target="_blank"
                  to="https://support.apple.com/en-us/102665"
                >
                  <SafariSvg />
                </Link>
                <Link
                  target="_blank"
                  to="https://www.microsoft.com/en-us/edge/download?form=MA13FJ"
                >
                  <EdgeSvg />
                </Link>
              </div>
              <button
                onClick={() => window.location.reload()}
                type="button"
                className="mt-8 w-full max-w-[200px] rounded-lg bg-primary py-2 max-sm:mx-auto"
              >
                <p className="text-[16px] font-[700] text-white">Refresh</p>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // In development, only check if we have user data (bypasses Keycloak)
  // In production, require full authentication
  const shouldAllowAccess = isDev ? !!user : isAuthenticated;

  // Skip authentication check in development
  if (!shouldAllowAccess && !isDev) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  return <>{children}</>;
};
