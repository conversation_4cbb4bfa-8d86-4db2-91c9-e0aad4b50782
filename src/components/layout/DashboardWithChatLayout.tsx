import React, { MutableRefObject, ReactNode, useEffect, useRef } from 'react';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { ConnectionFlowContext } from '@/types/businessStack';

interface DashboardWithChatLayoutProps {
  children: ReactNode;
  connectionFlow?: ConnectionFlowContext;
  reloadChatHistoryRef?: MutableRefObject<(() => Promise<void>) | null>;
  externalMessage?: string;
  className?: string;
  chatClassName?: string;
}

/**
 * Reusable dashboard layout with integrated chat sidebar
 *
 * Features:
 * - Fixed width chat sidebar that doesn't shrink
 * - Proper overflow handling for main content
 * - Flexible content area with scroll support
 * - Chat integration with connection flows
 */
export const DashboardWithChatLayout: React.FC<
  DashboardWithChatLayoutProps
> = ({
  children,
  connectionFlow,
  reloadChatHistoryRef,
  externalMessage,
  className = '',
  chatClassName = '',
}) => {
  return (
    <div className={`flex h-[calc(100vh-70px)] overflow-hidden ${className}`}>
      {/* Chat Sidebar - Fixed width, won't shrink */}
      <EnhancedChatSidebar
        connectionFlow={connectionFlow}
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalMessage={externalMessage}
        className={chatClassName}
      />

      {/* Main Content Area - Flexible, with proper overflow handling */}
      <div className="relative flex h-full min-w-0 flex-1 flex-col">
        <div className="min-h-0 flex-1 overflow-y-auto">{children}</div>
      </div>
    </div>
  );
};

/**
 * Reusable container for table pages that prevents horizontal overflow
 *
 * Features:
 * - Horizontal scroll for tables that exceed container width
 * - Maintains table within viewport boundaries
 * - Consistent padding and spacing
 */
export const TablePageContainer: React.FC<{
  children: ReactNode;
  className?: string;
  minTableWidth?: string;
}> = ({ children, className = '', minTableWidth = 'min-w-full' }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      // Force scrollbar to always show when there's overflow
      const forceScrollbarVisibility = () => {
        // Find the table element (child of the w-max div)
        const wrapperDiv = container.firstElementChild as HTMLElement;
        const table = wrapperDiv?.querySelector('table') as HTMLElement;

        if (table && wrapperDiv) {
          // Check if the table is wider than the container
          const tableWidth = table.offsetWidth;
          const containerWidth = container.clientWidth;

          if (tableWidth > containerWidth) {
            // Force scrollbar to be permanently visible
            container.style.overflowX = 'scroll';
            container.style.setProperty('scrollbar-width', 'thin');
            container.style.setProperty('-ms-overflow-style', 'scrollbar');

            // Force WebKit browsers to show scrollbar permanently
            container.style.setProperty('-webkit-overflow-scrolling', 'auto');

            // Add custom CSS for persistent scrollbar visibility
            container.classList.add('force-scrollbar-visible');
          } else {
            container.style.overflowX = 'auto';
            container.style.removeProperty('scrollbar-width');
            container.style.removeProperty('-webkit-overflow-scrolling');
            container.style.removeProperty('-ms-overflow-style');
            container.classList.remove('force-scrollbar-visible');
          }
        }
      };

      // Use a timeout to ensure DOM is fully rendered
      const timeoutId = setTimeout(() => {
        forceScrollbarVisibility();
      }, 100);

      // Monitor for content changes
      const resizeObserver = new ResizeObserver(forceScrollbarVisibility);
      resizeObserver.observe(container);

      // Monitor for DOM changes (like when table data loads)
      const mutationObserver = new MutationObserver(() => {
        // Delay to ensure DOM updates are complete
        setTimeout(forceScrollbarVisibility, 50);
      });

      mutationObserver.observe(container, {
        childList: true,
        subtree: true,
        attributes: true,
      });

      // Observe the wrapper div and table for size changes
      if (container.firstElementChild) {
        resizeObserver.observe(container.firstElementChild);

        const table = container.querySelector('table');
        if (table) {
          resizeObserver.observe(table);
        }
      }

      return () => {
        clearTimeout(timeoutId);
        resizeObserver.disconnect();
        mutationObserver.disconnect();
      };
    }
  }, [children]);

  return (
    <div className={`px-6 ${className}`}>
      <div className="w-full overflow-x-auto scrollbar-track-gray-100 scrollbar-thumb-gray-300 scrollbar-thumb-rounded hover:scrollbar-thumb-gray-400">
        <div className={`w-max ${minTableWidth}`}>{children}</div>
      </div>
    </div>
  );
};

export default DashboardWithChatLayout;
