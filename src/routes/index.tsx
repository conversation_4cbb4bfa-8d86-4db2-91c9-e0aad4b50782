import { useKeycloak } from '@react-keycloak/web';
import React from 'react';
import { HelmetProvider } from 'react-helmet-async';
import { createBrowserRouter, Outlet, RouterProvider } from 'react-router-dom';

import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';
import { retryChunkLoad } from '@/utils/chunkErrorHandler';
import { isDevEnvironment } from '@/utils/helpers';

import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import DashboardShell from '../components/layout/DashboardShell';
// Layout Shells
import NavigationShell from '../components/layout/NavigationShell';
import { ErrorBoundary } from '../components/ui/ErrorBoundary';
import NotFoundPage from '../pages/NotFoundPage';
import { DashboardRoutes } from './DashboardRoutes';
// Route definitions
import { ShellRoutes } from './ShellRoutes';

// Standalone auth pages
const ResetPasswordPage = React.lazy(() =>
  retryChunkLoad(() =>
    import('../pages/ResetPasswordPage/ResetPassword').then(module => ({
      default: module.ResetPassword,
    }))
  )
);

const NewPasswordPage = React.lazy(() =>
  retryChunkLoad(() => import('../pages/NewPasswordPage'))
);

const RootLayout = () => (
  <HelmetProvider>
    <Outlet />
  </HelmetProvider>
);

const router = createBrowserRouter([
  {
    element: <RootLayout />,
    children: [
      {
        path: '/',
        element: <NavigationShell />,
        children: ShellRoutes,
      },
      {
        path: '/dashboard',
        element: (
          <ProtectedRoute>
            <DashboardShell />
          </ProtectedRoute>
        ),
        children: DashboardRoutes,
      },
      {
        path: '/reset-password',
        element: (
          <ErrorBoundary>
            <React.Suspense fallback={<MainLoaderSkeleton />}>
              <ResetPasswordPage />
            </React.Suspense>
          </ErrorBoundary>
        ),
      },
      {
        path: '/reset-password/:token',
        element: (
          <ErrorBoundary>
            <React.Suspense fallback={<MainLoaderSkeleton />}>
              <NewPasswordPage />
            </React.Suspense>
          </ErrorBoundary>
        ),
      },
      {
        path: '*',
        element: (
          <ErrorBoundary>
            <NotFoundPage />
          </ErrorBoundary>
        ),
      },
    ],
  },
]);

export const MainAppRoutes = () => {
  const { initialized } = useKeycloak();
  if (!initialized && !isDevEnvironment()) return <MainLoaderSkeleton />;
  return <RouterProvider router={router} />;
};

export default MainAppRoutes;
