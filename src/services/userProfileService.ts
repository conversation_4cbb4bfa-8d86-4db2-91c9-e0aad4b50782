import { AxiosInstance } from 'axios';

import { publicRequest } from '@/lib/axios/publicRequest';
import {
  ApiResponse,
  ChangePasswordRequest,
  UpdateAvatarPayload,
  UpdateUserInfoRequest,
  UserProfileResponse,
} from '@/types/user';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

class UserProfileService {
  private static instance: UserProfileService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticUserService}/accounts`;
  }

  public static getInstance(): UserProfileService {
    if (!UserProfileService.instance) {
      UserProfileService.instance = new UserProfileService();
    }
    return UserProfileService.instance;
  }

  async updateUserInfo(
    axiosInstance: AxiosInstance,
    payload: UpdateUserInfoRequest
  ): Promise<ApiResponse<UserProfileResponse>> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/update-user-info`,
      payload
    );
    return response.data;
  }

  async updateAvatar(
    axiosInstance: AxiosInstance,
    payload: UpdateAvatarPayload,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<string>> {
    const config =
      payload instanceof FormData
        ? {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / (progressEvent.total || 1)
              );
              onProgress?.(percentCompleted);
            },
          }
        : {};

    const response = await axiosInstance.put(
      `${this.BASE_URL}/update-avatar`,
      payload,
      config
    );
    return response.data;
  }

  async changePassword(
    axiosInstance: AxiosInstance,
    payload: ChangePasswordRequest
  ): Promise<ApiResponse<string>> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/change-password`,
      payload
    );
    return response.data;
  }

  async changeEmail(
    axiosInstance: AxiosInstance,
    _payload: { email: string }
  ): Promise<ApiResponse<string>> {
    // TODO: Implement when email change API is available
    throw new Error('Email change functionality not yet implemented');
  }

  async getUserFunctions(
    axiosInstance: AxiosInstance
  ): Promise<ApiResponse<string[]>> {
    const response = await axiosInstance.get(
      `${agenticUserService}/users/user-functions`
    );
    return response.data;
  }

  async requestEmailVerification(payload: {
    email: string;
  }): Promise<ApiResponse<string>> {
    const { email } = payload;
    const response = await publicRequest(BASE_URL).get(
      `${agenticUserService}/request-email-verification?email=${email}`
    );
    return response.data;
  }

  async resetPassword(payload: {
    token: string;
    newPassword: string;
  }): Promise<ApiResponse<string>> {
    const response = await publicRequest(BASE_URL).post(
      `${agenticUserService}/users/reset-password`,
      payload
    );
    return response.data;
  }

  async requestPasswordReset(payload: {
    email: string;
  }): Promise<ApiResponse<string>> {
    const { email } = payload;
    const response = await publicRequest(BASE_URL).get(
      `${agenticUserService}/users/request-password-reset?email=${email}`
    );
    return response.data;
  }
}

export default UserProfileService.getInstance();
