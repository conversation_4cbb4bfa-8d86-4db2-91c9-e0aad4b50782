export const isDevEnvironment = () => {
  const url = typeof window !== 'undefined' ? window.location.href : '';
  return (
    url.includes('localhost') ||
    url.includes('***********') ||
    url.includes('192.168.') ||
    url.includes('10.0.')
  );
};

export const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

export const phoneRegExp =
  /^((\+[1-9][ \\-]*)|(\\(\d\\)[ \\-]*)|(\d)[ \\-]*)*?\d?[ \\-]*\d?$/;
export const emailRegExp = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const emailReplaceCharacterRegex = (emailString: string) => {
  if (emailString.trim() === '') {
    return emailString;
  }
  const [username, domain] = emailString.split('@');
  const obfuscatedUsername =
    username.charAt(0) + '*'.repeat(username.length - 2) + username.slice(-1);
  const obfuscatedDomain =
    domain.charAt(0) + '*'.repeat(domain.length - 2) + domain.slice(-1);

  return obfuscatedUsername + '@' + obfuscatedDomain;
};
